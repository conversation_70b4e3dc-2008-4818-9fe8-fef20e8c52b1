/** Document */
<template>
  <div>
    <!-- Attachment -->
    <div class="secondLevel-header-title">
      {{ $t('quotation.OfferPreApprovalRemark') }}
    </div>

    <a-form-model :model="formData" :colon="false" layout="vertical" ref="searchForm">
      <a-row :gutter="24" justify="start" type="flex">
        <a-col :span="18" flex="flex-start">
          <a-form-model-item :label="$t('quotation.Competitors')" prop="Remark" labelAlign="left">
            <div class="textarea-wrapper">
              <a-textarea
                class="textarea"
                v-model="formData.competitor"
                :rows="3"
                :maxLength="upperLimit"
                :placeholder="$t('common.inputPlaceholder')"
              />
              <div class="text-count">
                <span
                  class="text-length"
                  :class="textLength(formData.competitor) >= upperLimit ? 'red-text' : ''"
                  >{{ textLength(formData.competitor) }}</span
                ><span class="text-length">/</span><span class="upper-limit">{{ upperLimit }}</span>
              </div>
            </div>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24" justify="start" type="flex">
        <a-col :span="18" flex="flex-start">
          <a-form-model-item
            :label="$t('quotation.CompetitorsOffers')"
            prop="Remark"
            labelAlign="left"
          >
            <div class="textarea-wrapper">
              <a-textarea
                class="textarea"
                v-model="formData.competitor_offer"
                :rows="3"
                :maxLength="upperLimit"
                :placeholder="$t('common.inputPlaceholder')"
              />
              <div class="text-count">
                <span
                  class="text-length"
                  :class="textLength(formData.competitor_offer) >= upperLimit ? 'red-text' : ''"
                  >{{ textLength(formData.competitor_offer) }}</span
                ><span class="text-length">/</span><span class="upper-limit">{{ upperLimit }}</span>
              </div>
            </div>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24" justify="start" type="flex">
        <a-col :span="18" flex="flex-start">
          <a-form-model-item
            :label="$t('quotation.ImpactIfLoseTheDeal')"
            prop="Remark"
            labelAlign="left"
          >
            <div class="textarea-wrapper">
              <a-textarea
                class="textarea"
                v-model="formData.impact"
                :rows="3"
                :maxLength="upperLimit"
                :placeholder="$t('common.inputPlaceholder')"
              />
              <div class="text-count">
                <span
                  class="text-length"
                  :class="textLength(formData.impact) >= upperLimit ? 'red-text' : ''"
                  >{{ textLength(formData.impact) }}</span
                ><span class="text-length">/</span><span class="upper-limit">{{ upperLimit }}</span>
              </div>
            </div>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24" justify="start" type="flex">
        <a-col :span="18" flex="flex-start">
          <a-form-model-item
            :label="$t('quotation.UpcomingBusinessOpportunities')"
            prop="Remark"
            labelAlign="left"
          >
            <div class="textarea-wrapper">
              <a-textarea
                class="textarea"
                v-model="formData.opportunity"
                :rows="3"
                :maxLength="upperLimit"
                :placeholder="$t('common.inputPlaceholder')"
              />
              <div class="text-count">
                <span
                  class="text-length"
                  :class="textLength(formData.opportunity) >= upperLimit ? 'red-text' : ''"
                  >{{ textLength(formData.opportunity) }}</span
                ><span class="text-length">/</span><span class="upper-limit">{{ upperLimit }}</span>
              </div>
            </div>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </div>
</template>

<script>
  export default {
    name: 'approveRemark',
    components: {},
    props: {
      // 审批备注信息
      approveRemark: {
        type: Object,
        default: () => {},
      },
    },
    watch: {
      approveRemark: {
        handler(val) {
          if (val) {
            this.formData = val;
          }
        },
        immediate: true,
        deep: true,
      },
    },
    data() {
      return {
        formData: {
          competitor: '', //竞争对手
          competitor_offer: '', //竞争对手的报价
          impact: '', //影响
          opportunity: '', //机会
        },
        upperLimit: 500, //字数限制
      };
    },
    mounted() {},
    methods: {
      // 输入数据的字数
      textLength(value) {
        if (typeof value === 'number') {
          return String(value).length;
        }
        return (value || '').length;
      },

      
      //订单报文信息
      getOrderInfo() {
        return {
          ORDER_REMARK_ITEM: [
            {
              ATTR_CODE: 'competitor',
              ATTR_VALUE: this.formData.competitor,
              MODIFY_TAG: '0',
            },
            {
              ATTR_CODE: 'competitor_offer',
              ATTR_VALUE: this.formData.competitor_offer,
              MODIFY_TAG: '0',
            },
            {
              ATTR_CODE: 'impact',
              ATTR_VALUE: this.formData.impact,
              MODIFY_TAG: '0',
            },
            {
              ATTR_CODE: 'opportunity',
              ATTR_VALUE: this.formData.opportunity,
              MODIFY_TAG: '0',
            },
          ],
        };
      },
    },
  };
</script>

<style lang="less" scoped>
  .textarea-wrapper {
    position: relative;
    display: block;
    .textarea {
      height: 190px;
      font-size: 14px;
      color: #333333;
      letter-spacing: 0;
      line-height: 20px;
      font-weight: 400;
    }
    .text-count {
      display: flex;
      justify-content: flex-end;
      .text-length {
        font-size: 14px;
        color: #333333;
        letter-spacing: 0;
        line-height: 20px;
        font-weight: 400;
      }
      .red-text {
        font-size: 14px;
        color: #e60017;
        letter-spacing: 0;
        line-height: 20px;
        font-weight: 400;
      }
      .upper-limit {
        font-size: 14px;
        color: #9e9e9e;
        letter-spacing: 0;
        line-height: 20px;
        font-weight: 400;
      }
    }
  }
</style>
